import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

// Schemas
import { City, CityDocument } from './schemas/city.schema';
import { Location, LocationDocument } from './schemas/location.schema';
import { Amenity, AmenityDocument } from './schemas/amenity.schema';
import { Builder, BuilderDocument } from './schemas/builder.schema';
import { Agent, AgentDocument } from './schemas/agent.schema';

// DTOs
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { CreateAmenityDto } from './dto/create-amenity.dto';
import { UpdateAmenityDto } from './dto/update-amenity.dto';
import { CreateBuilderDto } from './dto/create-builder.dto';
import { UpdateBuilderDto } from './dto/update-builder.dto';
import {
  QueryCitiesDto,
  QueryLocationsDto,
  QueryAmenitiesDto,
  QueryBuildersDto,
  QueryAgentsDto,
} from './dto/query-masters.dto';

/**
 * Masters service
 * Handles all master data operations for cities, locations, amenities, builders, and agents
 */
@Injectable()
export class MastersService {
  constructor(
    @InjectModel(City.name) private cityModel: Model<CityDocument>,
    @InjectModel(Location.name) private locationModel: Model<LocationDocument>,
    @InjectModel(Amenity.name) private amenityModel: Model<AmenityDocument>,
    @InjectModel(Builder.name) private builderModel: Model<BuilderDocument>,
    @InjectModel(Agent.name) private agentModel: Model<AgentDocument>,
  ) {}

  // ==================== CITIES ====================

  /**
   * Create a new city
   */
  async createCity(createCityDto: CreateCityDto, userId: string): Promise<City> {
    try {
      // Check if city already exists
      const existingCity = await this.cityModel.findOne({
        name: createCityDto.name,
        state: createCityDto.state,
      });

      if (existingCity) {
        throw new ConflictException('City with this name already exists in the state');
      }

      const city = new this.cityModel({
        ...createCityDto,
        createdBy: new Types.ObjectId(userId),
      });

      return await city.save();
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create city');
    }
  }

  /**
   * Get all cities with pagination and filtering
   */
  async findAllCities(queryDto: QueryCitiesDto) {
    const { page, limit, search, status, state, country, isPopular, sortBy, sortOrder } = queryDto;

    // Build filter object
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) {
      filter.status = status;
    }

    if (state) {
      filter.state = { $regex: state, $options: 'i' };
    }

    if (country) {
      filter.country = { $regex: country, $options: 'i' };
    }

    if (typeof isPopular === 'boolean') {
      filter.isPopular = isPopular;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [cities, total] = await Promise.all([
      this.cityModel
        .find(filter)
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.cityModel.countDocuments(filter),
    ]);

    return {
      cities,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Get city by ID
   */
  async findCityById(id: string): Promise<City> {
    try {
      const city = await this.cityModel
        .findById(id)
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .exec();

      if (!city) {
        throw new NotFoundException('City not found');
      }

      return city;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Invalid city ID');
    }
  }

  /**
   * Update city
   */
  async updateCity(id: string, updateCityDto: UpdateCityDto, userId: string): Promise<City> {
    try {
      const city = await this.cityModel
        .findByIdAndUpdate(
          id,
          { ...updateCityDto, updatedBy: new Types.ObjectId(userId) },
          { new: true, runValidators: true }
        )
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .exec();

      if (!city) {
        throw new NotFoundException('City not found');
      }

      return city;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to update city');
    }
  }

  /**
   * Delete city
   */
  async removeCity(id: string): Promise<void> {
    try {
      // Check if city has locations
      const locationCount = await this.locationModel.countDocuments({ cityId: id });
      if (locationCount > 0) {
        throw new BadRequestException('Cannot delete city with existing locations');
      }

      const result = await this.cityModel.findByIdAndDelete(id).exec();
      if (!result) {
        throw new NotFoundException('City not found');
      }
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to delete city');
    }
  }

  // ==================== LOCATIONS ====================

  /**
   * Create a new location
   */
  async createLocation(createLocationDto: CreateLocationDto, userId: string): Promise<Location> {
    try {
      // Check if city exists
      const city = await this.cityModel.findById(createLocationDto.cityId);
      if (!city) {
        throw new BadRequestException('City not found');
      }

      // Check if location already exists in the city
      const existingLocation = await this.locationModel.findOne({
        name: createLocationDto.name,
        cityId: createLocationDto.cityId,
      });

      if (existingLocation) {
        throw new ConflictException('Location with this name already exists in the city');
      }

      const location = new this.locationModel({
        ...createLocationDto,
        cityId: new Types.ObjectId(createLocationDto.cityId),
        createdBy: new Types.ObjectId(userId),
      });

      return await location.save();
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to create location');
    }
  }

  /**
   * Get all locations with pagination and filtering
   */
  async findAllLocations(queryDto: QueryLocationsDto) {
    const { page, limit, search, status, cityId, area, isPopular, sortBy, sortOrder } = queryDto;

    // Build filter object
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { area: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) {
      filter.status = status;
    }

    if (cityId) {
      filter.cityId = new Types.ObjectId(cityId);
    }

    if (area) {
      filter.area = { $regex: area, $options: 'i' };
    }

    if (typeof isPopular === 'boolean') {
      filter.isPopular = isPopular;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [locations, total] = await Promise.all([
      this.locationModel
        .find(filter)
        .populate('cityId', 'name state country')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.locationModel.countDocuments(filter),
    ]);

    return {
      locations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Get locations by city ID
   */
  async getLocationsByCity(cityId: string): Promise<Location[]> {
    return await this.locationModel
      .find({ cityId: new Types.ObjectId(cityId), status: 'active' })
      .sort({ sortOrder: 1, name: 1 })
      .exec();
  }

  // ==================== AMENITIES ====================

  /**
   * Create a new amenity
   */
  async createAmenity(createAmenityDto: CreateAmenityDto, userId: string): Promise<Amenity> {
    try {
      // Check if amenity already exists
      const existingAmenity = await this.amenityModel.findOne({
        name: createAmenityDto.name,
      });

      if (existingAmenity) {
        throw new ConflictException('Amenity with this name already exists');
      }

      const amenity = new this.amenityModel({
        ...createAmenityDto,
        createdBy: new Types.ObjectId(userId),
      });

      return await amenity.save();
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create amenity');
    }
  }

  /**
   * Get all amenities with pagination and filtering
   */
  async findAllAmenities(queryDto: QueryAmenitiesDto) {
    const { page, limit, search, status, category, tags, isPopular, sortBy, sortOrder } = queryDto;

    // Build filter object
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } },
        { aliases: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) {
      filter.status = status;
    }

    if (category) {
      filter.category = category;
    }

    if (tags) {
      const tagList = tags.split(',').map(t => t.trim());
      filter.tags = { $in: tagList };
    }

    if (typeof isPopular === 'boolean') {
      filter.isPopular = isPopular;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [amenities, total] = await Promise.all([
      this.amenityModel
        .find(filter)
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.amenityModel.countDocuments(filter),
    ]);

    return {
      amenities,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Get amenities by category
   */
  async getAmenitiesByCategory(category: string): Promise<Amenity[]> {
    return await this.amenityModel
      .find({ category, status: 'active' })
      .sort({ sortOrder: 1, name: 1 })
      .exec();
  }

  // ==================== BUILDERS ====================

  /**
   * Create a new builder
   */
  async createBuilder(createBuilderDto: CreateBuilderDto, userId: string): Promise<Builder> {
    try {
      // Check if builder already exists
      const existingBuilder = await this.builderModel.findOne({
        name: createBuilderDto.name,
      });

      if (existingBuilder) {
        throw new ConflictException('Builder with this name already exists');
      }

      const builder = new this.builderModel({
        ...createBuilderDto,
        createdBy: new Types.ObjectId(userId),
      });

      return await builder.save();
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create builder');
    }
  }

  /**
   * Get all builders with pagination and filtering
   */
  async findAllBuilders(queryDto: QueryBuildersDto) {
    const {
      page,
      limit,
      search,
      status,
      type,
      city,
      operatingCities,
      isFeatured,
      isPopular,
      minRating,
      sortBy,
      sortOrder
    } = queryDto;

    // Build filter object
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { specializations: { $regex: search, $options: 'i' } },
        { operatingCities: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) {
      filter.status = status;
    }

    if (type) {
      filter.type = type;
    }

    if (city) {
      filter.city = { $regex: city, $options: 'i' };
    }

    if (operatingCities) {
      const cityList = operatingCities.split(',').map(c => c.trim());
      filter.operatingCities = { $in: cityList };
    }

    if (typeof isFeatured === 'boolean') {
      filter.isFeatured = isFeatured;
    }

    if (typeof isPopular === 'boolean') {
      filter.isPopular = isPopular;
    }

    if (minRating !== undefined) {
      filter.rating = { $gte: minRating };
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [builders, total] = await Promise.all([
      this.builderModel
        .find(filter)
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.builderModel.countDocuments(filter),
    ]);

    return {
      builders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Get master data statistics
   */
  async getStatistics() {
    const [
      totalCities,
      activeCities,
      totalLocations,
      activeLocations,
      totalAmenities,
      activeAmenities,
      totalBuilders,
      activeBuilders,
      totalAgents,
      activeAgents,
    ] = await Promise.all([
      this.cityModel.countDocuments(),
      this.cityModel.countDocuments({ status: 'active' }),
      this.locationModel.countDocuments(),
      this.locationModel.countDocuments({ status: 'active' }),
      this.amenityModel.countDocuments(),
      this.amenityModel.countDocuments({ status: 'active' }),
      this.builderModel.countDocuments(),
      this.builderModel.countDocuments({ status: 'active' }),
      this.agentModel.countDocuments(),
      this.agentModel.countDocuments({ status: 'active' }),
    ]);

    return {
      cities: { total: totalCities, active: activeCities },
      locations: { total: totalLocations, active: activeLocations },
      amenities: { total: totalAmenities, active: activeAmenities },
      builders: { total: totalBuilders, active: activeBuilders },
      agents: { total: totalAgents, active: activeAgents },
    };
  }
}
