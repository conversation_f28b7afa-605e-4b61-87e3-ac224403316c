import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { MasterStatus} from '../enums/master-type.enum';

/**
 * Builder document type for TypeScript
 */
export type BuilderDocument = Builder & Document;

/**
 * Builder schema for MongoDB
 * Manages builders that can be used in projects
 */
@Schema({
  timestamps: true,
  collection: 'builders',
})
export class Builder {
  @Prop({
    required: true,
    trim: true,
    unique: true,
    index: true,
  })
  name: string;

  @Prop({
    type: String,
   
    required: true,
    index: true,
  })
  

  @Prop({
    required: false,
    trim: true,
  })
  description?: string;

  @Prop({
    required: false,
    trim: true,
  })
  website?: string;

  @Prop({
    required: false,
    trim: true,
  })
  email?: string;

  @Prop({
    required: false,
    trim: true,
  })
  phone?: string;

  @Prop({
    required: false,
    trim: true,
  })
  address?: string;

  @Prop({
    required: false,
    trim: true,
  })
  city?: string;

  @Prop({
    required: false,
    trim: true,
  })
  state?: string;

  @Prop({
    required: false,
    trim: true,
  })
  country?: string;

  @Prop({
    required: false,
    trim: true,
  })
  pincode?: string;

  @Prop({
    required: false,
    trim: true,
  })
  logo?: string; // S3 URL for builder logo

  @Prop({
    required: false,
    trim: true,
  })
  registrationNumber?: string;

  @Prop({
    required: false,
    trim: true,
  })
  gstNumber?: string;

  @Prop({
    required: false,
    trim: true,
  })
  panNumber?: string;

  @Prop({
    required: false,
  })
  establishedYear?: number;

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  specializations?: string[]; // Residential, Commercial, etc.

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  operatingCities?: string[]; // Cities where builder operates

  @Prop({
    type: String,
    enum: MasterStatus,
    default: MasterStatus.ACTIVE,
    index: true,
  })
  status: MasterStatus;

  @Prop({
    default: 0,
  })
  sortOrder: number;

  @Prop({
    default: false,
  })
  isFeatured: boolean; // Featured builders

  @Prop({
    default: 0,
  })
  projectCount: number; // Number of projects by this builder

  @Prop({
    default: 0,
    min: 0,
    max: 5,
  })
  rating: number; // Builder rating (0-5)

  @Prop({
    default: 0,
  })
  reviewCount: number; // Number of reviews

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  awards?: string[]; // Awards and recognitions

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  certifications?: string[]; // ISO, RERA, etc.

  // Contact Person Details
  @Prop({
    required: false,
    trim: true,
  })
  contactPersonName?: string;

  @Prop({
    required: false,
    trim: true,
  })
  contactPersonDesignation?: string;

  @Prop({
    required: false,
    trim: true,
  })
  contactPersonEmail?: string;

  @Prop({
    required: false,
    trim: true,
  })
  contactPersonPhone?: string;

  // System fields
  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  createdBy: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: false,
  })
  updatedBy?: Types.ObjectId;

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Create and export the Builder schema
 */
export const BuilderSchema = SchemaFactory.createForClass(Builder);

// Add indexes for better query performance
BuilderSchema.index({ name: 1 }, { unique: true });
BuilderSchema.index({ type: 1 });
BuilderSchema.index({ status: 1 });
BuilderSchema.index({ isFeatured: 1 });
BuilderSchema.index({ city: 1 });
BuilderSchema.index({ operatingCities: 1 });
BuilderSchema.index({ rating: -1 });

// Text index for search
BuilderSchema.index({ 
  name: 'text', 
  description: 'text', 
  specializations: 'text',
  operatingCities: 'text'
});

// Pre-save middleware
BuilderSchema.pre('save', function(next) {
  // Convert name to title case
  if (this.isModified('name')) {
    this.name = this.name.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }
  next();
});
