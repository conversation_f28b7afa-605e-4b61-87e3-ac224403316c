import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { UserRole } from '../enums/user-role.enum';

/**
 * User document type for TypeScript
 */
export type UserDocument = User & Document;

/**
 * User schema for MongoDB
 * Defines the structure and validation rules for user documents
 */
@Schema({
  timestamps: true, // Automatically add createdAt and updatedAt fields
  collection: 'users', // Specify collection name
})
export class User {
  @Prop({
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    index: true, // Create index for faster queries
  })
  email: string;

  @Prop({
    required: true,
    minlength: 6,
  })
  password: string;

  @Prop({
    required: true,
    trim: true,
    minlength: 2,
    maxlength: 50,
  })
  firstName: string;

  @Prop({
    required: true,
    trim: true,
    minlength: 2,
    maxlength: 50,
  })
  lastName: string;

  @Prop({
    type: String,
    enum: UserRole,
    default: UserRole.USER,
    index: true, // Create index for role-based queries
  })
  role: UserRole;

  @Prop({
    required: false,
    trim: true,
    match: /^\+?[1-9]\d{1,14}$/, // Basic phone number validation
  })
  phoneNumber?: string;

  @Prop({
    required: false,
    trim: true,
    maxlength: 500,
  })
  bio?: string;

  @Prop({
    required: false,
    trim: true,
  })
  profilePicture?: string;

  @Prop({
    default: true,
    index: true, // Create index for active user queries
  })
  isActive: boolean;

  @Prop({
    default: false,
  })
  isEmailVerified: boolean;

  @Prop({
    required: false,
  })
  emailVerificationToken?: string;

  @Prop({
    required: false,
  })
  passwordResetToken?: string;

  @Prop({
    required: false,
  })
  passwordResetExpires?: Date;

  @Prop({
    required: false,
  })
  lastLoginAt?: Date;

  @Prop({
    required: false,
    trim: true,
  })
  lastLoginIP?: string;

  // Virtual field for full name
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  // Timestamps (automatically added by Mongoose)
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Create and export the User schema
 */
export const UserSchema = SchemaFactory.createForClass(User);

// Add virtual field for full name
UserSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Ensure virtual fields are serialized
UserSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret.password; // Never return password in JSON
    delete ret.emailVerificationToken;
    delete ret.passwordResetToken;
    delete ret.passwordResetExpires;
    return ret;
  },
});

// Add indexes for better query performance
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });
UserSchema.index({ createdAt: -1 });

// Pre-save middleware for additional validation or processing
UserSchema.pre('save', function(next) {
  // Update lastLoginAt when user logs in
  if (this.isModified('lastLoginAt')) {
    this.lastLoginAt = new Date();
  }
  next();
});
