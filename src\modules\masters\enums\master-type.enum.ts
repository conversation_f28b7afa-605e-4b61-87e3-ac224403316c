/**
 * Master data type enumeration
 * Defines different types of master data that can be managed
 */
export enum MasterType {
  CITY = 'city',
  LOCATION = 'location',
  AMENITY = 'amenity',
  BUILDER = 'builder',
  AGENT = 'agent',
  PROJECT_STATUS = 'project_status',
  PROPERTY_TYPE = 'property_type',
  UNIT_TYPE = 'unit_type',
  FACING_DIRECTION = 'facing_direction',
  POSSESSION_STATUS = 'possession_status',
  APPROVAL_STATUS = 'approval_status',
}

/**
 * Amenity category enumeration
 */
export enum AmenityCategory {
  BASIC = 'basic',
  SECURITY = 'security',
  RECREATIONAL = 'recreational',
  CONVENIENCE = 'convenience',
  CONNECTIVITY = 'connectivity',
  SPORTS = 'sports',
  WELLNESS = 'wellness',
  ENTERTAINMENT = 'entertainment',
  BUSINESS = 'business',
  ECO_FRIENDLY = 'eco_friendly',
}

/**
 * Master data status enumeration
 */
export enum MasterStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  ARCHIVED = 'archived',
}

/**
 * Builder type enumeration
 */
export enum BuilderType {
  INDIVIDUAL = 'individual',
  PARTNERSHIP = 'partnership',
  PRIVATE_LIMITED = 'private_limited',
  PUBLIC_LIMITED = 'public_limited',
  LLP = 'llp',
  COOPERATIVE = 'cooperative',
  GOVERNMENT = 'government',
}

/**
 * Agent type enumeration
 */
export enum AgentType {
  INDIVIDUAL = 'individual',
  BROKER = 'broker',
  CONSULTANT = 'consultant',
  CHANNEL_PARTNER = 'channel_partner',
  DIRECT_SALES = 'direct_sales',
}
