import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { MasterStatus } from '../enums/master-type.enum';

/**
 * City document type for TypeScript
 */
export type CityDocument = City & Document;

/**
 * City schema for MongoDB
 * Manages cities that can be used in projects
 */
@Schema({
  timestamps: true,
  collection: 'cities',
})
export class City {
  @Prop({
    required: true,
    trim: true,
    unique: true,
    index: true,
  })
  name: string;

  @Prop({
    required: true,
    trim: true,
  })
  state: string;

  @Prop({
    required: true,
    trim: true,
  })
  country: string;

  @Prop({
    required: false,
    trim: true,
  })
  code?: string; // City code (e.g., MUM for Mumbai)

  @Prop({
    required: false,
    trim: true,
  })
  description?: string;

  @Prop({
    required: false,
    type: [Number],
    validate: [arrayLimit, 'Coordinates must have exactly 2 elements'],
  })
  coordinates?: [number, number]; // [longitude, latitude]

  @Prop({
    required: false,
    trim: true,
  })
  timezone?: string;

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  pinCodes?: string[]; // Common pin codes in this city

  @Prop({
    type: String,
    enum: MasterStatus,
    default: MasterStatus.ACTIVE,
    index: true,
  })
  status: MasterStatus;

  @Prop({
    default: 0,
  })
  sortOrder: number; // For custom sorting

  @Prop({
    default: false,
  })
  isPopular: boolean; // Mark popular cities

  @Prop({
    default: 0,
  })
  projectCount: number; // Number of projects in this city

  // System fields
  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  createdBy: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: false,
  })
  updatedBy?: Types.ObjectId;

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

// Validation function for coordinates array
function arrayLimit(val: number[]) {
  return val.length === 2;
}

/**
 * Create and export the City schema
 */
export const CitySchema = SchemaFactory.createForClass(City);

// Add indexes for better query performance
CitySchema.index({ name: 1, state: 1 }, { unique: true });
CitySchema.index({ status: 1 });
CitySchema.index({ isPopular: 1 });
CitySchema.index({ sortOrder: 1 });
CitySchema.index({ coordinates: '2dsphere' }); // For geospatial queries

// Pre-save middleware
CitySchema.pre('save', function(next) {
  // Convert name to title case
  if (this.isModified('name')) {
    this.name = this.name.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }
  next();
});
