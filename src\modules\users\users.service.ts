import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from './schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { UserRole } from './enums/user-role.enum';

/**
 * Users service
 * Handles all user-related business logic and database operations
 */
@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  /**
   * Create a new user
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    try {
      // Check if user with email already exists
      const existingUser = await this.userModel.findOne({ 
        email: createUserDto.email.toLowerCase() 
      });

      if (existingUser) {
        throw new ConflictException('User with this email already exists');
      }

      // Create new user
      const createdUser = new this.userModel({
        ...createUserDto,
        email: createUserDto.email.toLowerCase(),
        role: createUserDto.role || UserRole.USER,
      });

      return await createdUser.save();
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create user');
    }
  }

  /**
   * Find all users with pagination and filtering
   */
  async findAll(queryDto: QueryUserDto) {
    const { page, limit, search, role, isActive, isEmailVerified, sortBy, sortOrder } = queryDto;
    
    // Build filter object
    const filter: any = {};
    
    if (search) {
      filter.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (role) {
      filter.role = role;
    }
    
    if (typeof isActive === 'boolean') {
      filter.isActive = isActive;
    }
    
    if (typeof isEmailVerified === 'boolean') {
      filter.isEmailVerified = isEmailVerified;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [users, total] = await Promise.all([
      this.userModel
        .find(filter)
        .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.userModel.countDocuments(filter),
    ]);

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Find user by ID
   */
  async findById(id: string): Promise<User> {
    try {
      const user = await this.userModel
        .findById(id)
        .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires')
        .exec();

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Invalid user ID');
    }
  }

  /**
   * Find user by email (used for authentication)
   */
  async findByEmail(email: string): Promise<UserDocument | null> {
    return await this.userModel.findOne({ 
      email: email.toLowerCase() 
    }).exec();
  }

  /**
   * Update user by ID
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    try {
      const user = await this.userModel
        .findByIdAndUpdate(
          id,
          { ...updateUserDto, updatedAt: new Date() },
          { new: true, runValidators: true }
        )
        .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires')
        .exec();

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to update user');
    }
  }

  /**
   * Delete user by ID (soft delete by setting isActive to false)
   */
  async remove(id: string): Promise<void> {
    try {
      const user = await this.userModel
        .findByIdAndUpdate(
          id,
          { isActive: false, updatedAt: new Date() },
          { new: true }
        )
        .exec();

      if (!user) {
        throw new NotFoundException('User not found');
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to delete user');
    }
  }
