import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Empty, <PERSON><PERSON><PERSON>, Min<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for user login
 * Validates email and password input for authentication
 */
export class LoginDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    format: 'email',
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({
    description: 'User password',
    example: 'SecurePassword123!',
    minLength: 6,
  })
  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string;
}
