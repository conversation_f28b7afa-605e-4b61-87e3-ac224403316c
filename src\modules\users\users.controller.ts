import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Query,
  UseGuards,
  Request
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ResponseUtil } from '../../common/utils/response.util';
import { UserRole } from './enums/user-role.enum';
import {
  ApiSuccessResponse,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
} from '../../common/decorators/api-response.decorator';

/**
 * Users controller
 * Handles HTTP requests for user management operations
 */
@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Create a new user
   */
  @Post()
  @ApiOperation({ 
    summary: 'Create User', 
    description: 'Create a new user account (Admin only)' 
  })
  @ApiCreatedResponse({
    description: 'User created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'User created successfully' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            firstName: { type: 'string' },
            lastName: { type: 'string' },
            role: { type: 'string' },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' },
          },
        },
        timestamp: { type: 'string' },
      },
    },
  })
  @ApiBadRequestResponse('Invalid input data or user already exists')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async create(@Body() createUserDto: CreateUserDto) {
    const user = await this.usersService.create(createUserDto);
    return ResponseUtil.created(user, 'User created successfully');
  }

  /**
   * Get all users with pagination and filtering
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get All Users', 
    description: 'Retrieve all users with pagination and filtering options' 
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'role', required: false, enum: UserRole, description: 'Filter by role' })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean, description: 'Filter by active status' })
  @ApiSuccessResponse({
    description: 'Users retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Users retrieved successfully' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              email: { type: 'string' },
              firstName: { type: 'string' },
              lastName: { type: 'string' },
              role: { type: 'string' },
              isActive: { type: 'boolean' },
              createdAt: { type: 'string' },
              updatedAt: { type: 'string' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
            hasNext: { type: 'boolean' },
            hasPrev: { type: 'boolean' },
          },
        },
        timestamp: { type: 'string' },
      },
    },
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async findAll(@Query() queryDto: QueryUserDto) {
    const result = await this.usersService.findAll(queryDto);
    return ResponseUtil.paginated(
      result.users,
      result.pagination.page,
      result.pagination.limit,
      result.pagination.total,
      'Users retrieved successfully'
    );
  }

  /**
   * Get user by ID
   */
  @Get(':id')
  @ApiOperation({ 
    summary: 'Get User by ID', 
    description: 'Retrieve a specific user by their ID' 
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiSuccessResponse({
    description: 'User retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'User retrieved successfully' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            firstName: { type: 'string' },
            lastName: { type: 'string' },
            role: { type: 'string' },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' },
          },
        },
        timestamp: { type: 'string' },
      },
    },
  })
  @ApiNotFoundResponse('User not found')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async findOne(@Param('id') id: string) {
    const user = await this.usersService.findById(id);
    return ResponseUtil.success(user, 'User retrieved successfully');
  }

  /**
   * Update user by ID
   */
  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update User', 
    description: 'Update user information by ID' 
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiSuccessResponse({
    description: 'User updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'User updated successfully' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            firstName: { type: 'string' },
            lastName: { type: 'string' },
            role: { type: 'string' },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' },
          },
        },
        timestamp: { type: 'string' },
      },
    },
  })
  @ApiBadRequestResponse('Invalid input data')
  @ApiNotFoundResponse('User not found')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    const user = await this.usersService.update(id, updateUserDto);
    return ResponseUtil.success(user, 'User updated successfully');
  }

  /**
   * Delete user by ID (soft delete)
   */
  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete User', 
    description: 'Soft delete user by setting isActive to false' 
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiSuccessResponse({
    description: 'User deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'User deleted successfully' },
        timestamp: { type: 'string' },
      },
    },
  })
  @ApiNotFoundResponse('User not found')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async remove(@Param('id') id: string) {
    await this.usersService.remove(id);
    return ResponseUtil.successMessage('User deleted successfully');
  }

  /**
   * Get user statistics
   */
  @Get('admin/statistics')
  @ApiOperation({ 
    summary: 'Get User Statistics', 
    description: 'Get comprehensive user statistics (Admin only)' 
  })
  @ApiSuccessResponse({
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Statistics retrieved successfully' },
        data: {
          type: 'object',
          properties: {
            totalUsers: { type: 'number' },
            activeUsers: { type: 'number' },
            inactiveUsers: { type: 'number' },
            verifiedUsers: { type: 'number' },
            unverifiedUsers: { type: 'number' },
            roleDistribution: { type: 'object' },
          },
        },
        timestamp: { type: 'string' },
      },
    },
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async getStatistics() {
    const stats = await this.usersService.getStatistics();
    return ResponseUtil.success(stats, 'Statistics retrieved successfully');
  }
}
