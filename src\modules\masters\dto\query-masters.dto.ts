import { <PERSON><PERSON><PERSON>al, IsEnum, IsString, IsBoolean, Is<PERSON><PERSON>ber, <PERSON>, <PERSON>, IsMongoId } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { MasterStatus, MasterType, AmenityCategory, BuilderType, AgentType } from '../enums/master-type.enum';

/**
 * Base query DTO for masters with common pagination and filtering
 */
export class QueryMastersDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Page must be a number' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Limit must be a number' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must not exceed 100' })
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Search term for name or description',
    example: 'mumbai',
  })
  @IsOptional()
  @IsString({ message: 'Search must be a string' })
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by status',
    enum: MasterStatus,
    example: MasterStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(MasterStatus, { message: 'Status must be a valid master status' })
  status?: MasterStatus;

  @ApiPropertyOptional({
    description: 'Filter by popular items only',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'isPopular must be a boolean value' })
  isPopular?: boolean;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'name',
    enum: ['name', 'createdAt', 'updatedAt', 'sortOrder'],
  })
  @IsOptional()
  @IsString({ message: 'Sort field must be a string' })
  sortBy?: string = 'sortOrder';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'asc',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsString({ message: 'Sort order must be a string' })
  sortOrder?: 'asc' | 'desc' = 'asc';
}

/**
 * Query DTO for cities
 */
export class QueryCitiesDto extends QueryMastersDto {
  @ApiPropertyOptional({
    description: 'Filter by state',
    example: 'Maharashtra',
  })
  @IsOptional()
  @IsString({ message: 'State must be a string' })
  state?: string;

  @ApiPropertyOptional({
    description: 'Filter by country',
    example: 'India',
  })
  @IsOptional()
  @IsString({ message: 'Country must be a string' })
  country?: string;
}

/**
 * Query DTO for locations
 */
export class QueryLocationsDto extends QueryMastersDto {
  @ApiPropertyOptional({
    description: 'Filter by city ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsOptional()
  @IsMongoId({ message: 'City ID must be a valid MongoDB ObjectId' })
  cityId?: string;

  @ApiPropertyOptional({
    description: 'Filter by area',
    example: 'Linking Road',
  })
  @IsOptional()
  @IsString({ message: 'Area must be a string' })
  area?: string;
}

/**
 * Query DTO for amenities
 */
export class QueryAmenitiesDto extends QueryMastersDto {
  @ApiPropertyOptional({
    description: 'Filter by amenity category',
    enum: AmenityCategory,
    example: AmenityCategory.BASIC,
  })
  @IsOptional()
  @IsEnum(AmenityCategory, { message: 'Category must be a valid amenity category' })
  category?: AmenityCategory;

  @ApiPropertyOptional({
    description: 'Filter by tags (comma-separated)',
    example: 'pool,water,recreation',
  })
  @IsOptional()
  @IsString({ message: 'Tags must be a string' })
  tags?: string;
}

/**
 * Query DTO for builders
 */
export class QueryBuildersDto extends QueryMastersDto {
  @ApiPropertyOptional({
    description: 'Filter by builder type',
    enum: BuilderType,
    example: BuilderType.PRIVATE_LIMITED,
  })
  @IsOptional()
  @IsEnum(BuilderType, { message: 'Type must be a valid builder type' })
  type?: BuilderType;

  @ApiPropertyOptional({
    description: 'Filter by city',
    example: 'Mumbai',
  })
  @IsOptional()
  @IsString({ message: 'City must be a string' })
  city?: string;

  @ApiPropertyOptional({
    description: 'Filter by operating cities (comma-separated)',
    example: 'Mumbai,Pune,Nashik',
  })
  @IsOptional()
  @IsString({ message: 'Operating cities must be a string' })
  operatingCities?: string;

  @ApiPropertyOptional({
    description: 'Filter by featured builders only',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'isFeatured must be a boolean value' })
  isFeatured?: boolean;

  @ApiPropertyOptional({
    description: 'Minimum rating filter',
    example: 4,
    minimum: 0,
    maximum: 5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Minimum rating must be a number' })
  @Min(0, { message: 'Minimum rating must be at least 0' })
  @Max(5, { message: 'Minimum rating must not exceed 5' })
  minRating?: number;
}

/**
 * Query DTO for agents
 */
export class QueryAgentsDto extends QueryMastersDto {
  @ApiPropertyOptional({
    description: 'Filter by agent type',
    enum: AgentType,
    example: AgentType.BROKER,
  })
  @IsOptional()
  @IsEnum(AgentType, { message: 'Type must be a valid agent type' })
  type?: AgentType;

  @ApiPropertyOptional({
    description: 'Filter by city',
    example: 'Mumbai',
  })
  @IsOptional()
  @IsString({ message: 'City must be a string' })
  city?: string;

  @ApiPropertyOptional({
    description: 'Filter by operating areas (comma-separated)',
    example: 'Bandra,Andheri,Juhu',
  })
  @IsOptional()
  @IsString({ message: 'Operating areas must be a string' })
  operatingAreas?: string;

  @ApiPropertyOptional({
    description: 'Filter by featured agents only',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'isFeatured must be a boolean value' })
  isFeatured?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by verified agents only',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'isVerified must be a boolean value' })
  isVerified?: boolean;

  @ApiPropertyOptional({
    description: 'Minimum rating filter',
    example: 4,
    minimum: 0,
    maximum: 5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Minimum rating must be a number' })
  @Min(0, { message: 'Minimum rating must be at least 0' })
  @Max(5, { message: 'Minimum rating must not exceed 5' })
  minRating?: number;
}
