import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';

/**
 * Data Transfer Object for updating a user
 * Extends CreateUserDto but makes all fields optional and excludes password
 * Password updates should be handled through a separate endpoint for security
 */
export class UpdateUserDto extends PartialType(
  OmitType(CreateUserDto, ['password', 'email'] as const)
) {
  // All fields from CreateUserDto are now optional except password and email
  // Email updates might require email verification, so excluded for now
  // Password updates should go through a separate secure endpoint
}
