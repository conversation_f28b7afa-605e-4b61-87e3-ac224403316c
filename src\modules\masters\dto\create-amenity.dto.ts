import {
  Is<PERSON><PERSON>,
  IsNotEmpty,
  IsOptional,
  IsArray,
  IsNumber,
  IsBoolean,
  IsEnum,
  MaxLength,
  <PERSON><PERSON><PERSON><PERSON>,
  Min,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { MasterStatus, AmenityCategory } from '../enums/master-type.enum';

/**
 * Data Transfer Object for creating a new amenity
 */
export class CreateAmenityDto {
  @ApiProperty({
    description: 'Amenity name',
    example: 'Swimming Pool',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Amenity category',
    enum: AmenityCategory,
    example: AmenityCategory.BASIC,
  })
  @IsEnum(AmenityCategory)
  @IsNotEmpty()
  category: AmenityCategory;

  @ApiPropertyOptional({
    description: 'Amenity description',
    example: 'Olympic size swimming pool with separate kids pool',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({
    description: 'Icon class or URL for UI',
    example: 'fas fa-swimming-pool',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  icon?: string;

  @ApiPropertyOptional({
    description: 'Amenity status',
    enum: MasterStatus,
    default: MasterStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(MasterStatus)
  status?: MasterStatus;

  @ApiPropertyOptional({
    description: 'Sort order within category',
    example: 1,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;

  @ApiPropertyOptional({
    description: 'Mark as popular amenity',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isPopular?: boolean;

  @ApiPropertyOptional({
    description: 'Additional tags for searching',
    example: ['pool', 'water', 'recreation'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Alternative names for this amenity',
    example: ['Pool', 'Swimming Area'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  aliases?: string[];
}
