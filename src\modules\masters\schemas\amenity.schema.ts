import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { MasterStatus} from '../enums/master-type.enum';

/**
 * Amenity document type for TypeScript
 */
export type AmenityDocument = Amenity & Document;

/**
 * Amenity schema for MongoDB
 * Manages amenities that can be used in projects
 */
@Schema({
  timestamps: true,
  collection: 'amenities',
})
export class Amenity {
  @Prop({
    required: true,
    trim: true,
    unique: true,
    index: true,
  })
  name: string;

  @Prop({
    type: String,
    enum: AmenityCategory,
    required: true,
    index: true,
  })
  category: AmenityCategory;

  @Prop({
    required: false,
    trim: true,
  })
  description?: string;

  @Prop({
    required: false,
    trim: true,
  })
  icon?: string; // Icon class or URL for UI

  @Prop({
    type: String,
    enum: MasterStatus,
    default: MasterStatus.ACTIVE,
    index: true,
  })
  status: MasterStatus;

  @Prop({
    default: 0,
  })
  sortOrder: number; // For custom sorting within category

  @Prop({
    default: false,
  })
  isPopular: boolean; // Mark popular amenities

  @Prop({
    default: 0,
  })
  usageCount: number; // Number of projects using this amenity

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  tags?: string[]; // Additional tags for searching

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  aliases?: string[]; // Alternative names for this amenity

  // System fields
  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  createdBy: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: false,
  })
  updatedBy?: Types.ObjectId;

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Create and export the Amenity schema
 */
export const AmenitySchema = SchemaFactory.createForClass(Amenity);

// Add indexes for better query performance
AmenitySchema.index({ name: 1 }, { unique: true });
AmenitySchema.index({ category: 1 });
AmenitySchema.index({ status: 1 });
AmenitySchema.index({ isPopular: 1 });
AmenitySchema.index({ sortOrder: 1 });
AmenitySchema.index({ tags: 1 });

// Text index for search
AmenitySchema.index({ 
  name: 'text', 
  description: 'text', 
  tags: 'text', 
  aliases: 'text' 
});

// Pre-save middleware
AmenitySchema.pre('save', function(next) {
  // Convert name to title case
  if (this.isModified('name')) {
    this.name = this.name.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }
  next();
});
