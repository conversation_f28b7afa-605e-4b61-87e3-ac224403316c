import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MastersController } from './masters.controller';
import { MastersService } from './masters.service';

// Schemas
import { City, CitySchema } from './schemas/city.schema';
import { Location, LocationSchema } from './schemas/location.schema';
import { Amenity, AmenitySchema } from './schemas/amenity.schema';
import { Builder, BuilderSchema } from './schemas/builder.schema';
import { Agent, AgentSchema } from './schemas/agent.schema';

/**
 * Masters module
 * Handles master data management for cities, locations, amenities, builders, and agents
 * Provides centralized control over configurable fields used in projects
 */
@Module({
  imports: [
    // Register all master data schemas with Mongoose
    MongooseModule.forFeature([
      { name: City.name, schema: CitySchema },
      { name: Location.name, schema: LocationSchema },
      { name: Amenity.name, schema: AmenitySchema },
      { name: Builder.name, schema: BuilderSchema },
      { name: Agent.name, schema: AgentSchema },
    ]),
  ],
  controllers: [MastersController],
  providers: [MastersService],
  exports: [MastersService], // Export service for use in other modules (like ProjectsModule)
})
export class MastersModule {}
