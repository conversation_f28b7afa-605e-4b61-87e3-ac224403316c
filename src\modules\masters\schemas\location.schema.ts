import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { MasterStatus } from '../enums/master-type.enum';

/**
 * Location document type for TypeScript
 */
export type LocationDocument = Location & Document;

/**
 * Location schema for MongoDB
 * Manages locations within cities that can be used in projects
 */
@Schema({
  timestamps: true,
  collection: 'locations',
})
export class Location {
  @Prop({
    required: true,
    trim: true,
    index: true,
  })
  name: string;

  @Prop({
    type: Types.ObjectId,
    ref: 'City',
    required: true,
    index: true,
  })
  cityId: Types.ObjectId;

  @Prop({
    required: false,
    trim: true,
  })
  area?: string; // Area/Zone within the location

  @Prop({
    required: false,
    trim: true,
  })
  description?: string;

  @Prop({
    required: false,
    type: [Number],
    validate: [arrayLimit, 'Coordinates must have exactly 2 elements'],
  })
  coordinates?: [number, number]; // [longitude, latitude]

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  pinCodes?: string[]; // Pin codes for this location

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  landmarks?: string[]; // Famous landmarks in this location

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  connectivity?: string[]; // Metro stations, bus stops, etc.

  @Prop({
    type: String,
    enum: MasterStatus,
    default: MasterStatus.ACTIVE,
    index: true,
  })
  status: MasterStatus;

  @Prop({
    default: 0,
  })
  sortOrder: number; // For custom sorting

  @Prop({
    default: false,
  })
  isPopular: boolean; // Mark popular locations

  @Prop({
    default: 0,
  })
  projectCount: number; // Number of projects in this location

  @Prop({
    required: false,
    min: 0,
  })
  averagePrice?: number; // Average price per sq ft in this location

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  nearbyFacilities?: string[]; // Schools, hospitals, malls, etc.

  // System fields
  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  createdBy: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: false,
  })
  updatedBy?: Types.ObjectId;

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

// Validation function for coordinates array
function arrayLimit(val: number[]) {
  return val.length === 2;
}

/**
 * Create and export the Location schema
 */
export const LocationSchema = SchemaFactory.createForClass(Location);

// Add indexes for better query performance
LocationSchema.index({ name: 1, cityId: 1 }, { unique: true });
LocationSchema.index({ cityId: 1 });
LocationSchema.index({ status: 1 });
LocationSchema.index({ isPopular: 1 });
LocationSchema.index({ sortOrder: 1 });
LocationSchema.index({ coordinates: '2dsphere' }); // For geospatial queries

// Pre-save middleware
LocationSchema.pre('save', function(next) {
  // Convert name to title case
  if (this.isModified('name')) {
    this.name = this.name.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }
  next();
});
