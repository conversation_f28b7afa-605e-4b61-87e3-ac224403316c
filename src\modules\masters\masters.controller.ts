import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { MastersService } from './masters.service';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { CreateAmenityDto } from './dto/create-amenity.dto';
import { UpdateAmenityDto } from './dto/update-amenity.dto';
import { CreateBuilderDto } from './dto/create-builder.dto';
import { UpdateBuilderDto } from './dto/update-builder.dto';
import {
  QueryCitiesDto,
  QueryLocationsDto,
  QueryAmenitiesDto,
  QueryBuildersDto,
} from './dto/query-masters.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ResponseUtil } from '../../common/utils/response.util';
import {
  ApiSuccessResponse,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
} from '../../common/decorators/api-response.decorator';

/**
 * Masters controller
 * Handles HTTP requests for master data management (cities, locations, amenities, builders, agents)
 */
@ApiTags('Masters')
@Controller('masters')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class MastersController {
  constructor(private readonly mastersService: MastersService) {}

  // ==================== CITIES ====================

  /**
   * Create a new city
   */
  @Post('cities')
  @ApiOperation({
    summary: 'Create City',
    description: 'Create a new city for use in projects',
  })
  @ApiCreatedResponse({
    description: 'City created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'City created successfully' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            state: { type: 'string' },
            country: { type: 'string' },
            code: { type: 'string' },
            status: { type: 'string' },
            isPopular: { type: 'boolean' },
            createdAt: { type: 'string' },
          },
        },
        timestamp: { type: 'string' },
      },
    },
  })
  @ApiBadRequestResponse('Invalid input data or city already exists')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async createCity(@Body() createCityDto: CreateCityDto, @Request() req) {
    const city = await this.mastersService.createCity(createCityDto, req.user._id);
    return ResponseUtil.created(city, 'City created successfully');
  }

  /**
   * Get all cities
   */
  @Get('cities')
  @ApiOperation({
    summary: 'Get All Cities',
    description: 'Retrieve all cities with pagination and filtering',
  })
  @ApiSuccessResponse({
    description: 'Cities retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Cities retrieved successfully' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              state: { type: 'string' },
              country: { type: 'string' },
              code: { type: 'string' },
              status: { type: 'string' },
              isPopular: { type: 'boolean' },
              projectCount: { type: 'number' },
              createdAt: { type: 'string' },
            },
          },
        },
        pagination: { type: 'object' },
        timestamp: { type: 'string' },
      },
    },
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async findAllCities(@Query() queryDto: QueryCitiesDto) {
    const result = await this.mastersService.findAllCities(queryDto);
    return ResponseUtil.paginated(
      result.cities,
      result.pagination.page,
      result.pagination.limit,
      result.pagination.total,
      'Cities retrieved successfully'
    );
  }

  /**
   * Get city by ID
   */
  @Get('cities/:id')
  @ApiOperation({
    summary: 'Get City by ID',
    description: 'Retrieve a specific city by its ID',
  })
  @ApiParam({ name: 'id', description: 'City ID' })
  @ApiSuccessResponse({
    description: 'City retrieved successfully',
  })
  @ApiNotFoundResponse('City not found')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async findCityById(@Param('id') id: string) {
    const city = await this.mastersService.findCityById(id);
    return ResponseUtil.success(city, 'City retrieved successfully');
  }

  /**
   * Update city
   */
  @Patch('cities/:id')
  @ApiOperation({
    summary: 'Update City',
    description: 'Update city information by ID',
  })
  @ApiParam({ name: 'id', description: 'City ID' })
  @ApiSuccessResponse({
    description: 'City updated successfully',
  })
  @ApiBadRequestResponse('Invalid input data')
  @ApiNotFoundResponse('City not found')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async updateCity(
    @Param('id') id: string,
    @Body() updateCityDto: UpdateCityDto,
    @Request() req
  ) {
    const city = await this.mastersService.updateCity(id, updateCityDto, req.user._id);
    return ResponseUtil.success(city, 'City updated successfully');
  }

  /**
   * Delete city
   */
  @Delete('cities/:id')
  @ApiOperation({
    summary: 'Delete City',
    description: 'Delete city by ID (only if no locations exist)',
  })
  @ApiParam({ name: 'id', description: 'City ID' })
  @ApiSuccessResponse({
    description: 'City deleted successfully',
  })
  @ApiNotFoundResponse('City not found')
  @ApiBadRequestResponse('Cannot delete city with existing locations')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async removeCity(@Param('id') id: string) {
    await this.mastersService.removeCity(id);
    return ResponseUtil.successMessage('City deleted successfully');
  }

  // ==================== LOCATIONS ====================

  /**
   * Create a new location
   */
  @Post('locations')
  @ApiOperation({
    summary: 'Create Location',
    description: 'Create a new location within a city',
  })
  @ApiCreatedResponse({
    description: 'Location created successfully',
  })
  @ApiBadRequestResponse('Invalid input data or location already exists')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async createLocation(@Body() createLocationDto: CreateLocationDto, @Request() req) {
    const location = await this.mastersService.createLocation(createLocationDto, req.user._id);
    return ResponseUtil.created(location, 'Location created successfully');
  }

  /**
   * Get all locations
   */
  @Get('locations')
  @ApiOperation({
    summary: 'Get All Locations',
    description: 'Retrieve all locations with pagination and filtering',
  })
  @ApiSuccessResponse({
    description: 'Locations retrieved successfully',
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async findAllLocations(@Query() queryDto: QueryLocationsDto) {
    const result = await this.mastersService.findAllLocations(queryDto);
    return ResponseUtil.paginated(
      result.locations,
      result.pagination.page,
      result.pagination.limit,
      result.pagination.total,
      'Locations retrieved successfully'
    );
  }

  /**
   * Get locations by city
   */
  @Get('locations/city/:cityId')
  @ApiOperation({
    summary: 'Get Locations by City',
    description: 'Get all active locations for a specific city',
  })
  @ApiParam({ name: 'cityId', description: 'City ID' })
  @ApiSuccessResponse({
    description: 'Locations retrieved successfully',
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async getLocationsByCity(@Param('cityId') cityId: string) {
    const locations = await this.mastersService.getLocationsByCity(cityId);
    return ResponseUtil.success(locations, 'Locations retrieved successfully');
  }

  // ==================== AMENITIES ====================

  /**
   * Create a new amenity
   */
  @Post('amenities')
  @ApiOperation({
    summary: 'Create Amenity',
    description: 'Create a new amenity for use in projects',
  })
  @ApiCreatedResponse({
    description: 'Amenity created successfully',
  })
  @ApiBadRequestResponse('Invalid input data or amenity already exists')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async createAmenity(@Body() createAmenityDto: CreateAmenityDto, @Request() req) {
    const amenity = await this.mastersService.createAmenity(createAmenityDto, req.user._id);
    return ResponseUtil.created(amenity, 'Amenity created successfully');
  }

  /**
   * Get all amenities
   */
  @Get('amenities')
  @ApiOperation({
    summary: 'Get All Amenities',
    description: 'Retrieve all amenities with pagination and filtering',
  })
  @ApiSuccessResponse({
    description: 'Amenities retrieved successfully',
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async findAllAmenities(@Query() queryDto: QueryAmenitiesDto) {
    const result = await this.mastersService.findAllAmenities(queryDto);
    return ResponseUtil.paginated(
      result.amenities,
      result.pagination.page,
      result.pagination.limit,
      result.pagination.total,
      'Amenities retrieved successfully'
    );
  }

  /**
   * Get amenities by category
   */
  @Get('amenities/category/:category')
  @ApiOperation({
    summary: 'Get Amenities by Category',
    description: 'Get all active amenities for a specific category',
  })
  @ApiParam({ name: 'category', description: 'Amenity category' })
  @ApiSuccessResponse({
    description: 'Amenities retrieved successfully',
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async getAmenitiesByCategory(@Param('category') category: string) {
    const amenities = await this.mastersService.getAmenitiesByCategory(category);
    return ResponseUtil.success(amenities, 'Amenities retrieved successfully');
  }

  // ==================== BUILDERS ====================

  /**
   * Create a new builder
   */
  @Post('builders')
  @ApiOperation({
    summary: 'Create Builder',
    description: 'Create a new builder for use in projects',
  })
  @ApiCreatedResponse({
    description: 'Builder created successfully',
  })
  @ApiBadRequestResponse('Invalid input data or builder already exists')
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async createBuilder(@Body() createBuilderDto: CreateBuilderDto, @Request() req) {
    const builder = await this.mastersService.createBuilder(createBuilderDto, req.user._id);
    return ResponseUtil.created(builder, 'Builder created successfully');
  }

  /**
   * Get all builders
   */
  @Get('builders')
  @ApiOperation({
    summary: 'Get All Builders',
    description: 'Retrieve all builders with pagination and filtering',
  })
  @ApiSuccessResponse({
    description: 'Builders retrieved successfully',
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async findAllBuilders(@Query() queryDto: QueryBuildersDto) {
    const result = await this.mastersService.findAllBuilders(queryDto);
    return ResponseUtil.paginated(
      result.builders,
      result.pagination.page,
      result.pagination.limit,
      result.pagination.total,
      'Builders retrieved successfully'
    );
  }

  /**
   * Get master data statistics
   */
  @Get('statistics')
  @ApiOperation({
    summary: 'Get Master Data Statistics',
    description: 'Get comprehensive statistics for all master data',
  })
  @ApiSuccessResponse({
    description: 'Statistics retrieved successfully',
  })
  @ApiUnauthorizedResponse('Invalid or missing JWT token')
  async getStatistics() {
    const stats = await this.mastersService.getStatistics();
    return ResponseUtil.success(stats, 'Statistics retrieved successfully');
  }
}
