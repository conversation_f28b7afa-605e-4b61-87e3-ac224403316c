import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { MasterStatus, AgentType } from '../enums/master-type.enum';

/**
 * Agent document type for TypeScript
 */
export type AgentDocument = Agent & Document;

/**
 * Agent schema for MongoDB
 * Manages agents/brokers that can be used in projects
 */
@Schema({
  timestamps: true,
  collection: 'agents',
})
export class Agent {
  @Prop({
    required: true,
    trim: true,
    index: true,
  })
  name: string;

  @Prop({
    type: String,
    enum: AgentType,
    required: true,
    index: true,
  })
  type: AgentType;

  @Prop({
    required: true,
    trim: true,
    unique: true,
    index: true,
  })
  email: string;

  @Prop({
    required: true,
    trim: true,
  })
  phone: string;

  @Prop({
    required: false,
    trim: true,
  })
  alternatePhone?: string;

  @Prop({
    required: false,
    trim: true,
  })
  whatsappNumber?: string;

  @Prop({
    required: false,
    trim: true,
  })
  companyName?: string;

  @Prop({
    required: false,
    trim: true,
  })
  designation?: string;

  @Prop({
    required: false,
    trim: true,
  })
  address?: string;

  @Prop({
    required: false,
    trim: true,
  })
  city?: string;

  @Prop({
    required: false,
    trim: true,
  })
  state?: string;

  @Prop({
    required: false,
    trim: true,
  })
  country?: string;

  @Prop({
    required: false,
    trim: true,
  })
  pincode?: string;

  @Prop({
    required: false,
    trim: true,
  })
  profilePicture?: string; // S3 URL for agent photo

  @Prop({
    required: false,
    trim: true,
  })
  reraNumber?: string; // RERA registration number

  @Prop({
    required: false,
    trim: true,
  })
  licenseNumber?: string; // Broker license number

  @Prop({
    required: false,
  })
  experienceYears?: number;

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  specializations?: string[]; // Residential, Commercial, Rental, etc.

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  operatingAreas?: string[]; // Areas where agent operates

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  languages?: string[]; // Languages spoken

  @Prop({
    type: String,
    enum: MasterStatus,
    default: MasterStatus.ACTIVE,
    index: true,
  })
  status: MasterStatus;

  @Prop({
    default: 0,
  })
  sortOrder: number;

  @Prop({
    default: false,
  })
  isFeatured: boolean; // Featured agents

  @Prop({
    default: false,
  })
  isVerified: boolean; // Verified agents

  @Prop({
    default: 0,
  })
  projectCount: number; // Number of projects handled

  @Prop({
    default: 0,
  })
  dealCount: number; // Number of deals closed

  @Prop({
    default: 0,
    min: 0,
    max: 5,
  })
  rating: number; // Agent rating (0-5)

  @Prop({
    default: 0,
  })
  reviewCount: number; // Number of reviews

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  certifications?: string[]; // Professional certifications

  @Prop({
    required: false,
    type: [String],
    default: [],
  })
  achievements?: string[]; // Awards and achievements

  // Commission Structure
  @Prop({
    required: false,
    min: 0,
    max: 100,
  })
  commissionPercentage?: number; // Commission percentage

  @Prop({
    required: false,
    min: 0,
  })
  fixedCommission?: number; // Fixed commission amount

  // Social Media Links
  @Prop({
    required: false,
    trim: true,
  })
  linkedinProfile?: string;

  @Prop({
    required: false,
    trim: true,
  })
  facebookProfile?: string;

  @Prop({
    required: false,
    trim: true,
  })
  instagramProfile?: string;

  @Prop({
    required: false,
    trim: true,
  })
  twitterProfile?: string;

  // System fields
  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  createdBy: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: false,
  })
  updatedBy?: Types.ObjectId;

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Create and export the Agent schema
 */
export const AgentSchema = SchemaFactory.createForClass(Agent);

// Add indexes for better query performance
AgentSchema.index({ email: 1 }, { unique: true });
AgentSchema.index({ name: 1 });
AgentSchema.index({ type: 1 });
AgentSchema.index({ status: 1 });
AgentSchema.index({ isFeatured: 1 });
AgentSchema.index({ isVerified: 1 });
AgentSchema.index({ city: 1 });
AgentSchema.index({ operatingAreas: 1 });
AgentSchema.index({ rating: -1 });
AgentSchema.index({ phone: 1 });

// Text index for search
AgentSchema.index({ 
  name: 'text', 
  companyName: 'text', 
  specializations: 'text',
  operatingAreas: 'text'
});

// Pre-save middleware
AgentSchema.pre('save', function(next) {
  // Convert name to title case
  if (this.isModified('name')) {
    this.name = this.name.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }
  
  // Convert email to lowercase
  if (this.isModified('email')) {
    this.email = this.email.toLowerCase();
  }
  
  next();
});
