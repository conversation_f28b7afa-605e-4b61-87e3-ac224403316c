import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { City, CityDocument } from '../schemas/city.schema';
import { Location, LocationDocument } from '../schemas/location.schema';
import { Amenity, AmenityDocument } from '../schemas/amenity.schema';
import { Builder, BuilderDocument } from '../schemas/builder.schema';
import { MasterStatus, AmenityCategory, BuilderType } from '../enums/master-type.enum';

/**
 * Masters data seeder
 * Seeds initial master data for cities, locations, amenities, and builders
 */
@Injectable()
export class MastersSeeder {
  constructor(
    @InjectModel(City.name) private cityModel: Model<CityDocument>,
    @InjectModel(Location.name) private locationModel: Model<LocationDocument>,
    @InjectModel(Amenity.name) private amenityModel: Model<AmenityDocument>,
    @InjectModel(Builder.name) private builderModel: Model<BuilderDocument>,
  ) {}

  /**
   * Seed all master data
   */
  async seedAll(userId: string): Promise<void> {
    console.log('Starting master data seeding...');
    
    await this.seedCities(userId);
    await this.seedAmenities(userId);
    await this.seedBuilders(userId);
    
    console.log('Master data seeding completed!');
  }

  /**
   * Seed cities data
   */
  async seedCities(userId: string): Promise<void> {
    const cities = [
      {
        name: 'Mumbai',
        state: 'Maharashtra',
        country: 'India',
        code: 'MUM',
        description: 'Financial capital of India',
        coordinates: [72.8777, 19.0760],
        timezone: 'Asia/Kolkata',
        pinCodes: ['400001', '400002', '400003', '400004', '400005'],
        isPopular: true,
        sortOrder: 1,
      },
      {
        name: 'Pune',
        state: 'Maharashtra',
        country: 'India',
        code: 'PUN',
        description: 'IT hub and cultural center',
        coordinates: [73.8567, 18.5204],
        timezone: 'Asia/Kolkata',
        pinCodes: ['411001', '411002', '411003', '411004', '411005'],
        isPopular: true,
        sortOrder: 2,
      },
      {
        name: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        code: 'BLR',
        description: 'Silicon Valley of India',
        coordinates: [77.5946, 12.9716],
        timezone: 'Asia/Kolkata',
        pinCodes: ['560001', '560002', '560003', '560004', '560005'],
        isPopular: true,
        sortOrder: 3,
      },
      {
        name: 'Delhi',
        state: 'Delhi',
        country: 'India',
        code: 'DEL',
        description: 'Capital city of India',
        coordinates: [77.1025, 28.7041],
        timezone: 'Asia/Kolkata',
        pinCodes: ['110001', '110002', '110003', '110004', '110005'],
        isPopular: true,
        sortOrder: 4,
      },
      {
        name: 'Gurgaon',
        state: 'Haryana',
        country: 'India',
        code: 'GGN',
        description: 'Millennium city and financial hub',
        coordinates: [77.0266, 28.4595],
        timezone: 'Asia/Kolkata',
        pinCodes: ['122001', '122002', '122003', '122004', '122005'],
        isPopular: true,
        sortOrder: 5,
      },
    ];

    for (const cityData of cities) {
      const existingCity = await this.cityModel.findOne({
        name: cityData.name,
        state: cityData.state,
      });

      if (!existingCity) {
        await this.cityModel.create({
          ...cityData,
          createdBy: userId,
          status: MasterStatus.ACTIVE,
        });
        console.log(`Created city: ${cityData.name}`);
      }
    }
  }

  /**
   * Seed amenities data
   */
  async seedAmenities(userId: string): Promise<void> {
    const amenities = [
      // Basic Amenities
      { name: 'Swimming Pool', category: AmenityCategory.BASIC, icon: 'fas fa-swimming-pool', isPopular: true, sortOrder: 1 },
      { name: 'Gymnasium', category: AmenityCategory.BASIC, icon: 'fas fa-dumbbell', isPopular: true, sortOrder: 2 },
      { name: 'Garden', category: AmenityCategory.BASIC, icon: 'fas fa-seedling', isPopular: true, sortOrder: 3 },
      { name: 'Parking', category: AmenityCategory.BASIC, icon: 'fas fa-parking', isPopular: true, sortOrder: 4 },
      { name: 'Elevator', category: AmenityCategory.BASIC, icon: 'fas fa-elevator', isPopular: true, sortOrder: 5 },

      // Security Amenities
      { name: 'CCTV Surveillance', category: AmenityCategory.SECURITY, icon: 'fas fa-video', isPopular: true, sortOrder: 1 },
      { name: '24/7 Security', category: AmenityCategory.SECURITY, icon: 'fas fa-shield-alt', isPopular: true, sortOrder: 2 },
      { name: 'Intercom', category: AmenityCategory.SECURITY, icon: 'fas fa-phone', isPopular: false, sortOrder: 3 },
      { name: 'Access Control', category: AmenityCategory.SECURITY, icon: 'fas fa-key', isPopular: false, sortOrder: 4 },

      // Recreational Amenities
      { name: 'Club House', category: AmenityCategory.RECREATIONAL, icon: 'fas fa-home', isPopular: true, sortOrder: 1 },
      { name: 'Children Play Area', category: AmenityCategory.RECREATIONAL, icon: 'fas fa-child', isPopular: true, sortOrder: 2 },
      { name: 'Jogging Track', category: AmenityCategory.RECREATIONAL, icon: 'fas fa-running', isPopular: true, sortOrder: 3 },
      { name: 'Tennis Court', category: AmenityCategory.RECREATIONAL, icon: 'fas fa-table-tennis', isPopular: false, sortOrder: 4 },
      { name: 'Basketball Court', category: AmenityCategory.RECREATIONAL, icon: 'fas fa-basketball-ball', isPopular: false, sortOrder: 5 },

      // Convenience Amenities
      { name: 'Power Backup', category: AmenityCategory.CONVENIENCE, icon: 'fas fa-bolt', isPopular: true, sortOrder: 1 },
      { name: 'Water Supply', category: AmenityCategory.CONVENIENCE, icon: 'fas fa-tint', isPopular: true, sortOrder: 2 },
      { name: 'Waste Management', category: AmenityCategory.CONVENIENCE, icon: 'fas fa-recycle', isPopular: false, sortOrder: 3 },
      { name: 'Maintenance Service', category: AmenityCategory.CONVENIENCE, icon: 'fas fa-tools', isPopular: false, sortOrder: 4 },

      // Connectivity Amenities
      { name: 'Metro Station', category: AmenityCategory.CONNECTIVITY, icon: 'fas fa-subway', isPopular: true, sortOrder: 1 },
      { name: 'Bus Stop', category: AmenityCategory.CONNECTIVITY, icon: 'fas fa-bus', isPopular: true, sortOrder: 2 },
      { name: 'Highway Access', category: AmenityCategory.CONNECTIVITY, icon: 'fas fa-road', isPopular: true, sortOrder: 3 },
      { name: 'Airport Connectivity', category: AmenityCategory.CONNECTIVITY, icon: 'fas fa-plane', isPopular: false, sortOrder: 4 },

      // Sports Amenities
      { name: 'Badminton Court', category: AmenityCategory.SPORTS, icon: 'fas fa-shuttlecock', isPopular: false, sortOrder: 1 },
      { name: 'Squash Court', category: AmenityCategory.SPORTS, icon: 'fas fa-racquet', isPopular: false, sortOrder: 2 },
      { name: 'Cricket Pitch', category: AmenityCategory.SPORTS, icon: 'fas fa-baseball-ball', isPopular: false, sortOrder: 3 },

      // Wellness Amenities
      { name: 'Spa', category: AmenityCategory.WELLNESS, icon: 'fas fa-spa', isPopular: false, sortOrder: 1 },
      { name: 'Yoga Center', category: AmenityCategory.WELLNESS, icon: 'fas fa-meditation', isPopular: false, sortOrder: 2 },
      { name: 'Meditation Hall', category: AmenityCategory.WELLNESS, icon: 'fas fa-om', isPopular: false, sortOrder: 3 },

      // Entertainment Amenities
      { name: 'Movie Theater', category: AmenityCategory.ENTERTAINMENT, icon: 'fas fa-film', isPopular: false, sortOrder: 1 },
      { name: 'Game Room', category: AmenityCategory.ENTERTAINMENT, icon: 'fas fa-gamepad', isPopular: false, sortOrder: 2 },
      { name: 'Library', category: AmenityCategory.ENTERTAINMENT, icon: 'fas fa-book', isPopular: false, sortOrder: 3 },

      // Business Amenities
      { name: 'Business Center', category: AmenityCategory.BUSINESS, icon: 'fas fa-briefcase', isPopular: false, sortOrder: 1 },
      { name: 'Conference Room', category: AmenityCategory.BUSINESS, icon: 'fas fa-users', isPopular: false, sortOrder: 2 },
      { name: 'Co-working Space', category: AmenityCategory.BUSINESS, icon: 'fas fa-laptop', isPopular: false, sortOrder: 3 },

      // Eco-Friendly Amenities
      { name: 'Solar Panels', category: AmenityCategory.ECO_FRIENDLY, icon: 'fas fa-solar-panel', isPopular: false, sortOrder: 1 },
      { name: 'Rainwater Harvesting', category: AmenityCategory.ECO_FRIENDLY, icon: 'fas fa-cloud-rain', isPopular: false, sortOrder: 2 },
      { name: 'Organic Waste Converter', category: AmenityCategory.ECO_FRIENDLY, icon: 'fas fa-leaf', isPopular: false, sortOrder: 3 },
    ];

    for (const amenityData of amenities) {
      const existingAmenity = await this.amenityModel.findOne({
        name: amenityData.name,
      });

      if (!existingAmenity) {
        await this.amenityModel.create({
          ...amenityData,
          createdBy: userId,
          status: MasterStatus.ACTIVE,
        });
        console.log(`Created amenity: ${amenityData.name}`);
      }
    }
  }

  /**
   * Seed builders data
   */
  async seedBuilders(userId: string): Promise<void> {
    const builders = [
      {
        name: 'Lodha Group',
        type: BuilderType.PRIVATE_LIMITED,
        description: 'Leading real estate developer in India with premium residential and commercial projects',
        website: 'https://lodhagroup.in',
        email: '<EMAIL>',
        phone: '+91-22-6777-7777',
        city: 'Mumbai',
        state: 'Maharashtra',
        country: 'India',
        establishedYear: 1980,
        specializations: ['Luxury Residential', 'Commercial', 'Retail'],
        operatingCities: ['Mumbai', 'Pune', 'London'],
        isFeatured: true,
        rating: 4.5,
        certifications: ['ISO 9001:2015', 'RERA Certified'],
        awards: ['Best Luxury Developer 2023', 'Excellence in Construction'],
        sortOrder: 1,
      },
      {
        name: 'Godrej Properties',
        type: BuilderType.PUBLIC_LIMITED,
        description: 'Trusted name in real estate with focus on sustainable development',
        website: 'https://godrejproperties.com',
        email: '<EMAIL>',
        phone: '+91-22-2518-8010',
        city: 'Mumbai',
        state: 'Maharashtra',
        country: 'India',
        establishedYear: 1990,
        specializations: ['Residential', 'Commercial', 'Plotted Development'],
        operatingCities: ['Mumbai', 'Pune', 'Bangalore', 'Delhi', 'Gurgaon'],
        isFeatured: true,
        rating: 4.3,
        certifications: ['IGBC Certified', 'RERA Certified'],
        awards: ['Green Building Excellence', 'Customer Satisfaction Award'],
        sortOrder: 2,
      },
      {
        name: 'Prestige Group',
        type: BuilderType.PRIVATE_LIMITED,
        description: 'South India\'s leading real estate developer with diverse portfolio',
        website: 'https://prestigeconstructions.com',
        email: '<EMAIL>',
        phone: '+91-80-4337-4337',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        establishedYear: 1986,
        specializations: ['Residential', 'Commercial', 'Retail', 'Hospitality'],
        operatingCities: ['Bangalore', 'Chennai', 'Hyderabad', 'Kochi', 'Goa'],
        isFeatured: true,
        rating: 4.2,
        certifications: ['ISO 14001:2015', 'RERA Certified'],
        awards: ['Developer of the Year 2023', 'Innovation in Design'],
        sortOrder: 3,
      },
    ];

    for (const builderData of builders) {
      const existingBuilder = await this.builderModel.findOne({
        name: builderData.name,
      });

      if (!existingBuilder) {
        await this.builderModel.create({
          ...builderData,
          createdBy: userId,
          status: MasterStatus.ACTIVE,
        });
        console.log(`Created builder: ${builderData.name}`);
      }
    }
  }
}
