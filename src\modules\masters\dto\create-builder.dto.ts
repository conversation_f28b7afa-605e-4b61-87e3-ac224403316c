import {
  <PERSON>S<PERSON>,
  IsNotEmpty,
  IsOptional,
  IsArray,
  IsNumber,
  IsBoolean,
  IsEnum,
  IsEmail,
  IsUrl,
  <PERSON>Length,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { MasterStatus, BuilderType } from '../enums/master-type.enum';

/**
 * Data Transfer Object for creating a new builder
 */
export class CreateBuilderDto {
  @ApiProperty({
    description: 'Builder name',
    example: 'ABC Constructions Pvt Ltd',
    minLength: 2,
    maxLength: 200,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  @MaxLength(200)
  name: string;

  @ApiProperty({
    description: 'Builder type',
    enum: BuilderType,
    example: BuilderType.PRIVATE_LIMITED,
  })
  @IsEnum(BuilderType)
  @IsNotEmpty()
  type: BuilderType;

  @ApiPropertyOptional({
    description: 'Builder description',
    example: 'Leading real estate developer with 20+ years of experience',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({
    description: 'Builder website',
    example: 'https://abcconstructions.com',
  })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiPropertyOptional({
    description: 'Builder email',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: 'Builder phone number',
    example: '+91-9876543210',
    maxLength: 20,
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string;

  @ApiPropertyOptional({
    description: 'Builder address',
    example: '123 Business District, Mumbai',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  address?: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'Mumbai',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  city?: string;

  @ApiPropertyOptional({
    description: 'State',
    example: 'Maharashtra',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  state?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'India',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  country?: string;

  @ApiPropertyOptional({
    description: 'Pincode',
    example: '400001',
    maxLength: 10,
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  pincode?: string;

  @ApiPropertyOptional({
    description: 'Builder logo URL',
    example: 'https://s3.amazonaws.com/bucket/builder-logo.jpg',
  })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiPropertyOptional({
    description: 'Registration number',
    example: 'REG123456789',
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  registrationNumber?: string;

  @ApiPropertyOptional({
    description: 'GST number',
    example: '27**********1Z5',
    maxLength: 15,
  })
  @IsOptional()
  @IsString()
  @MaxLength(15)
  gstNumber?: string;

  @ApiPropertyOptional({
    description: 'PAN number',
    example: '**********',
    maxLength: 10,
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  panNumber?: string;

  @ApiPropertyOptional({
    description: 'Year established',
    example: 2000,
    minimum: 1900,
    maximum: 2030,
  })
  @IsOptional()
  @IsNumber()
  @Min(1900)
  @Max(2030)
  establishedYear?: number;

  @ApiPropertyOptional({
    description: 'Builder specializations',
    example: ['Residential', 'Commercial', 'Luxury'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specializations?: string[];

  @ApiPropertyOptional({
    description: 'Cities where builder operates',
    example: ['Mumbai', 'Pune', 'Nashik'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  operatingCities?: string[];

  @ApiPropertyOptional({
    description: 'Builder status',
    enum: MasterStatus,
    default: MasterStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(MasterStatus)
  status?: MasterStatus;

  @ApiPropertyOptional({
    description: 'Sort order for display',
    example: 1,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;

  @ApiPropertyOptional({
    description: 'Mark as featured builder',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;

  @ApiPropertyOptional({
    description: 'Builder rating (0-5)',
    example: 4.5,
    minimum: 0,
    maximum: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  rating?: number;

  @ApiPropertyOptional({
    description: 'Awards and recognitions',
    example: ['Best Builder 2023', 'Excellence in Construction'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  awards?: string[];

  @ApiPropertyOptional({
    description: 'Certifications',
    example: ['ISO 9001:2015', 'RERA Certified'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  certifications?: string[];

  @ApiPropertyOptional({
    description: 'Contact person name',
    example: 'John Doe',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  contactPersonName?: string;

  @ApiPropertyOptional({
    description: 'Contact person designation',
    example: 'Sales Manager',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  contactPersonDesignation?: string;

  @ApiPropertyOptional({
    description: 'Contact person email',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  contactPersonEmail?: string;

  @ApiPropertyOptional({
    description: 'Contact person phone',
    example: '+91-9876543210',
    maxLength: 20,
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  contactPersonPhone?: string;
}
